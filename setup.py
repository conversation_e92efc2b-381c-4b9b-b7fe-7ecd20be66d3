#!/usr/bin/env python3
"""
Setup script for YouTube Forex Bot
"""

import os
import subprocess
import sys
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def install_requirements():
    """Install Python requirements."""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Python requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Error installing requirements: {e}")
        sys.exit(1)


def check_tesseract():
    """Check if Tesseract OCR is installed."""
    try:
        result = subprocess.run(["tesseract", "--version"], 
                              capture_output=True, text=True, check=True)
        print("✓ Tesseract OCR is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠ Tesseract OCR not found")
        print_tesseract_install_instructions()
        return False


def print_tesseract_install_instructions():
    """Print Tesseract installation instructions."""
    print("\nTesseract OCR Installation Instructions:")
    print("----------------------------------------")
    
    if sys.platform.startswith('linux'):
        print("Ubuntu/Debian: sudo apt-get install tesseract-ocr")
        print("CentOS/RHEL: sudo yum install tesseract")
    elif sys.platform == 'darwin':
        print("macOS: brew install tesseract")
    elif sys.platform.startswith('win'):
        print("Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
    
    print("\nAfter installation, run this setup script again.")


def create_directories():
    """Create necessary directories."""
    directories = [
        "data/screenshots",
        "data/debug",
        "data/downloads",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ Directories created")


def create_config_file():
    """Create local config file if it doesn't exist."""
    if not Path("config_local.yaml").exists():
        if Path("config.yaml").exists():
            import shutil
            shutil.copy("config.yaml", "config_local.yaml")
            print("✓ Local config file created (config_local.yaml)")
        else:
            print("⚠ Default config file not found")


def create_env_file():
    """Create .env file if it doesn't exist."""
    if not Path(".env").exists():
        if Path(".env.example").exists():
            import shutil
            shutil.copy(".env.example", ".env")
            print("✓ Environment file created (.env)")
            print("  Please edit .env file with your settings")
        else:
            print("⚠ .env.example file not found")


def check_chrome():
    """Check if Chrome/Chromium is available."""
    chrome_commands = ["google-chrome", "chromium-browser", "chrome", "chromium"]
    
    for cmd in chrome_commands:
        try:
            subprocess.run([cmd, "--version"], 
                         capture_output=True, check=True)
            print("✓ Chrome/Chromium browser found")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    print("⚠ Chrome/Chromium browser not found")
    print("Please install Google Chrome or Chromium browser")
    return False


def run_tests():
    """Run basic tests to verify installation."""
    print("\nRunning basic tests...")
    
    try:
        # Test imports
        import selenium
        import cv2
        import pytesseract
        import yaml
        import loguru
        print("✓ All required packages can be imported")
        
        # Test configuration loading
        from src.config.config_manager import ConfigManager
        config = ConfigManager()
        print("✓ Configuration manager works")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False


def main():
    """Main setup function."""
    print("YouTube Forex Bot Setup")
    print("=" * 30)
    
    # Check Python version
    check_python_version()
    
    # Install requirements
    install_requirements()
    
    # Check system dependencies
    tesseract_ok = check_tesseract()
    chrome_ok = check_chrome()
    
    # Create directories and files
    create_directories()
    create_config_file()
    create_env_file()
    
    # Run tests
    tests_ok = run_tests()
    
    print("\nSetup Summary:")
    print("-" * 20)
    print(f"Python requirements: ✓")
    print(f"Tesseract OCR: {'✓' if tesseract_ok else '⚠'}")
    print(f"Chrome browser: {'✓' if chrome_ok else '⚠'}")
    print(f"Basic tests: {'✓' if tests_ok else '✗'}")
    
    if tesseract_ok and chrome_ok and tests_ok:
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit config_local.yaml or .env with your settings")
        print("2. Run: python main.py")
        print("3. Check logs in logs/ directory")
    else:
        print("\n⚠ Setup completed with warnings")
        print("Please resolve the issues above before running the bot")
    
    print("\n⚠ IMPORTANT: Always test in demo mode first!")


if __name__ == "__main__":
    main()
