"""
Screen Capture Module for YouTube Forex Bot
Captures screenshots from YouTube live streams for signal extraction.
"""

import asyncio
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple
import io

import cv2
import numpy as np
from PIL import Image, ImageEnhance
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

from loguru import logger


class ScreenCapture:
    """Handles screen capture from YouTube live streams."""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.driver: Optional[webdriver.Chrome] = None
        self.current_stream_url: Optional[str] = None
        
        # Configuration
        self.save_screenshots = self.config.get('capture.save_screenshots', True)
        self.screenshot_dir = Path(self.config.get('capture.screenshot_dir', 'data/screenshots'))
        self.max_screenshots = self.config.get('capture.max_screenshots', 1000)
        self.image_quality = self.config.get('capture.image_quality', 95)
        self.resize_width = self.config.get('capture.resize_width', 1920)
        self.resize_height = self.config.get('capture.resize_height', 1080)
        
        # Create screenshot directory
        self.screenshot_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("Screen Capture initialized")
    
    async def initialize(self):
        """Initialize the screen capture system."""
        try:
            await self._setup_driver()
            logger.info("Screen Capture initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Screen Capture: {e}")
            raise
    
    async def _setup_driver(self):
        """Setup Chrome WebDriver for screen capture."""
        try:
            chrome_options = Options()
            
            # Configure for optimal video capture
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--mute-audio')
            chrome_options.add_argument('--autoplay-policy=no-user-gesture-required')
            
            # Set window size for consistent captures
            chrome_options.add_argument(f'--window-size={self.resize_width},{self.resize_height}')
            
            # Headless mode if configured
            if self.config.get('browser.headless', False):
                chrome_options.add_argument('--headless')
            
            # User agent
            user_agent = self.config.get('browser.user_agent')
            if user_agent:
                chrome_options.add_argument(f'--user-agent={user_agent}')
            
            # Setup service
            service = Service(ChromeDriverManager().install())
            
            # Create driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(30)
            
            logger.info("Screen capture WebDriver setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup screen capture WebDriver: {e}")
            raise
    
    async def set_stream_url(self, stream_url: str):
        """
        Set the current stream URL for capture.
        
        Args:
            stream_url: YouTube live stream URL
        """
        try:
            if stream_url != self.current_stream_url:
                self.current_stream_url = stream_url
                logger.info(f"Stream URL set: {stream_url}")
                
                # Navigate to stream and prepare for capture
                await self._prepare_stream_for_capture()
                
        except Exception as e:
            logger.error(f"Error setting stream URL: {e}")
    
    async def _prepare_stream_for_capture(self):
        """Prepare the stream page for optimal capture."""
        try:
            if not self.current_stream_url:
                return
            
            # Navigate to stream
            self.driver.get(self.current_stream_url)
            
            # Wait for video player to load
            await asyncio.sleep(5)
            
            # Handle any overlays or popups
            await self._handle_overlays()
            
            # Set video quality and fullscreen if needed
            await self._optimize_video_settings()
            
            logger.info("Stream prepared for capture")
            
        except Exception as e:
            logger.error(f"Error preparing stream for capture: {e}")
    
    async def _handle_overlays(self):
        """Handle YouTube overlays and popups."""
        try:
            # Close cookie consent
            consent_selectors = [
                "button[aria-label*='Accept']",
                "button[aria-label*='I agree']",
                "#yDmH0d button"
            ]
            
            for selector in consent_selectors:
                try:
                    element = WebDriverWait(self.driver, 2).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()
                    await asyncio.sleep(1)
                    break
                except:
                    continue
            
            # Close any video overlays
            overlay_selectors = [
                ".ytp-ad-skip-button",
                ".ytp-ad-overlay-close-button",
                ".ytp-suggested-action-badge-dismiss-button"
            ]
            
            for selector in overlay_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            element.click()
                            await asyncio.sleep(0.5)
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"Error handling overlays: {e}")
    
    async def _optimize_video_settings(self):
        """Optimize video settings for better capture."""
        try:
            # Click on video to focus
            video_element = self.driver.find_element(By.CSS_SELECTOR, "video")
            if video_element:
                video_element.click()
                await asyncio.sleep(1)
            
            # Try to set video quality to highest
            try:
                # Right-click on video to access context menu
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.driver)
                actions.context_click(video_element).perform()
                await asyncio.sleep(1)
                
                # Look for quality settings
                quality_elements = self.driver.find_elements(By.CSS_SELECTOR, "[role='menuitem']")
                for element in quality_elements:
                    if "quality" in element.text.lower():
                        element.click()
                        break
                        
            except:
                pass
            
            # Hide cursor from video area
            self.driver.execute_script("""
                var video = document.querySelector('video');
                if (video) {
                    video.style.cursor = 'none';
                }
            """)
            
        except Exception as e:
            logger.debug(f"Error optimizing video settings: {e}")
    
    async def capture_screenshot(self) -> Optional[bytes]:
        """
        Capture screenshot from current stream.
        
        Returns:
            Screenshot as bytes, or None if capture failed
        """
        try:
            if not self.driver or not self.current_stream_url:
                logger.warning("No driver or stream URL available for capture")
                return None
            
            # Take screenshot
            screenshot_png = self.driver.get_screenshot_as_png()
            
            # Process the screenshot
            processed_image = await self._process_screenshot(screenshot_png)
            
            # Save if configured
            if self.save_screenshots and processed_image:
                await self._save_screenshot(processed_image)
            
            # Clean up old screenshots
            await self._cleanup_old_screenshots()
            
            return processed_image
            
        except Exception as e:
            logger.error(f"Error capturing screenshot: {e}")
            return None
    
    async def _process_screenshot(self, screenshot_data: bytes) -> Optional[bytes]:
        """
        Process screenshot for better OCR results.
        
        Args:
            screenshot_data: Raw screenshot data
            
        Returns:
            Processed screenshot as bytes
        """
        try:
            # Convert to PIL Image
            image = Image.open(io.BytesIO(screenshot_data))
            
            # Resize if needed
            if image.size != (self.resize_width, self.resize_height):
                image = image.resize((self.resize_width, self.resize_height), Image.Resampling.LANCZOS)
            
            # Enhance image for better OCR
            if self.config.get('ocr.preprocessing.contrast_enhance', True):
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(1.2)
            
            if self.config.get('ocr.preprocessing.sharpen', True):
                enhancer = ImageEnhance.Sharpness(image)
                image = enhancer.enhance(1.1)
            
            # Convert to OpenCV format for additional processing
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Denoise if configured
            if self.config.get('ocr.preprocessing.denoise', True):
                cv_image = cv2.fastNlMeansDenoisingColored(cv_image, None, 10, 10, 7, 21)
            
            # Convert back to PIL and then to bytes
            processed_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))
            
            # Save to bytes
            output = io.BytesIO()
            processed_image.save(output, format='PNG', quality=self.image_quality)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error processing screenshot: {e}")
            return screenshot_data  # Return original if processing fails
    
    async def _save_screenshot(self, image_data: bytes):
        """Save screenshot to disk."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"screenshot_{timestamp}.png"
            filepath = self.screenshot_dir / filename
            
            with open(filepath, 'wb') as f:
                f.write(image_data)
            
            logger.debug(f"Screenshot saved: {filename}")
            
        except Exception as e:
            logger.error(f"Error saving screenshot: {e}")
    
    async def _cleanup_old_screenshots(self):
        """Remove old screenshots to maintain storage limits."""
        try:
            if not self.save_screenshots:
                return
            
            # Get all screenshot files
            screenshot_files = list(self.screenshot_dir.glob("screenshot_*.png"))
            
            # Sort by modification time (newest first)
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Remove excess files
            if len(screenshot_files) > self.max_screenshots:
                files_to_remove = screenshot_files[self.max_screenshots:]
                for file_path in files_to_remove:
                    try:
                        file_path.unlink()
                        logger.debug(f"Removed old screenshot: {file_path.name}")
                    except Exception as e:
                        logger.warning(f"Failed to remove {file_path}: {e}")
                        
        except Exception as e:
            logger.error(f"Error cleaning up screenshots: {e}")
    
    async def capture_video_element(self) -> Optional[bytes]:
        """
        Capture only the video element area.
        
        Returns:
            Cropped video screenshot as bytes
        """
        try:
            # Find video element
            video_element = self.driver.find_element(By.CSS_SELECTOR, "video")
            if not video_element:
                return await self.capture_screenshot()
            
            # Get video element location and size
            location = video_element.location
            size = video_element.size
            
            # Take full screenshot
            screenshot_png = self.driver.get_screenshot_as_png()
            
            # Crop to video area
            image = Image.open(io.BytesIO(screenshot_png))
            
            # Calculate crop box
            left = location['x']
            top = location['y']
            right = left + size['width']
            bottom = top + size['height']
            
            # Crop image
            cropped_image = image.crop((left, top, right, bottom))
            
            # Convert to bytes
            output = io.BytesIO()
            cropped_image.save(output, format='PNG', quality=self.image_quality)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error capturing video element: {e}")
            return await self.capture_screenshot()  # Fallback to full screenshot
    
    async def get_video_info(self) -> dict:
        """
        Get information about the current video.
        
        Returns:
            Dictionary with video information
        """
        try:
            info = {
                'url': self.current_stream_url,
                'title': '',
                'duration': '',
                'is_live': False,
                'viewer_count': ''
            }
            
            if not self.driver:
                return info
            
            # Get video title
            try:
                title_element = self.driver.find_element(By.CSS_SELECTOR, "h1.ytd-video-primary-info-renderer")
                info['title'] = title_element.text
            except:
                pass
            
            # Check if live
            try:
                live_elements = self.driver.find_elements(By.CSS_SELECTOR, ".badge-style-type-live-now")
                info['is_live'] = len(live_elements) > 0
            except:
                pass
            
            # Get viewer count
            try:
                viewer_element = self.driver.find_element(By.CSS_SELECTOR, ".view-count")
                info['viewer_count'] = viewer_element.text
            except:
                pass
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            return {}
    
    async def close(self):
        """Close the screen capture system."""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
            logger.info("Screen Capture closed")
        except Exception as e:
            logger.error(f"Error closing Screen Capture: {e}")
