"""
Safety Manager for YouTube Forex Bot
Implements safety checks and risk management.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass

from loguru import logger


@dataclass
class TradeRecord:
    """Record of a trade execution."""
    timestamp: datetime
    signal_type: str
    currency_pair: str
    entry_price: float
    lot_size: float
    profit_loss: Optional[float] = None
    status: str = "open"  # open, closed, cancelled


@dataclass
class ForexSignal:
    """Forex trading signal data structure."""
    currency_pair: str
    signal_type: str  # BUY or SELL
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    confidence: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class SafetyManager:
    """Manages trading safety and risk controls."""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.trade_history: List[TradeRecord] = []
        self.daily_loss = 0.0
        self.consecutive_losses = 0
        self.last_trade_time = None
        self.emergency_stop = False
        self.cooldown_until = None
        
        # Load safety settings
        self.max_daily_loss = self.config.get('safety.max_loss_per_day', 100.0)
        self.emergency_stop_loss = self.config.get('safety.emergency_stop_loss', 500.0)
        self.max_consecutive_losses = self.config.get('safety.max_consecutive_losses', 5)
        self.cooldown_period = self.config.get('safety.cooldown_period', 300)
        self.dry_run_mode = self.config.get('safety.dry_run_mode', True)
        self.require_confirmation = self.config.get('safety.require_confirmation', True)
        
        # Trading limits
        self.max_risk_per_trade = self.config.get('trading.max_risk_per_trade', 0.02)
        self.max_daily_trades = self.config.get('trading.max_daily_trades', 10)
        self.max_concurrent_trades = self.config.get('trading.max_concurrent_trades', 3)
        
        logger.info("Safety Manager initialized")
    
    def can_trade(self) -> bool:
        """
        Check if trading is allowed based on safety constraints.
        
        Returns:
            True if trading is allowed, False otherwise
        """
        try:
            # Check emergency stop
            if self.emergency_stop:
                logger.warning("Trading blocked: Emergency stop activated")
                return False
            
            # Check cooldown period
            if self.cooldown_until and datetime.now() < self.cooldown_until:
                logger.warning("Trading blocked: In cooldown period")
                return False
            
            # Check daily loss limit
            if self.daily_loss >= self.max_daily_loss:
                logger.warning(f"Trading blocked: Daily loss limit reached ({self.daily_loss})")
                return False
            
            # Check consecutive losses
            if self.consecutive_losses >= self.max_consecutive_losses:
                logger.warning(f"Trading blocked: Max consecutive losses reached ({self.consecutive_losses})")
                self._activate_cooldown()
                return False
            
            # Check daily trade limit
            today_trades = self._get_today_trades_count()
            if today_trades >= self.max_daily_trades:
                logger.warning(f"Trading blocked: Daily trade limit reached ({today_trades})")
                return False
            
            # Check concurrent trades
            open_trades = self._get_open_trades_count()
            if open_trades >= self.max_concurrent_trades:
                logger.warning(f"Trading blocked: Max concurrent trades reached ({open_trades})")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error in can_trade check: {e}")
            return False
    
    def validate_signal(self, signal: ForexSignal) -> bool:
        """
        Validate a forex signal before execution.
        
        Args:
            signal: Forex signal to validate
            
        Returns:
            True if signal is valid, False otherwise
        """
        try:
            # Check signal completeness
            if not signal.currency_pair or not signal.signal_type or not signal.entry_price:
                logger.warning("Signal validation failed: Missing required fields")
                return False
            
            # Check signal type
            if signal.signal_type not in ['BUY', 'SELL']:
                logger.warning(f"Signal validation failed: Invalid signal type {signal.signal_type}")
                return False
            
            # Check currency pair format
            if len(signal.currency_pair) != 6:
                logger.warning(f"Signal validation failed: Invalid currency pair {signal.currency_pair}")
                return False
            
            # Check price validity
            if signal.entry_price <= 0:
                logger.warning(f"Signal validation failed: Invalid entry price {signal.entry_price}")
                return False
            
            # Check stop loss and take profit
            if signal.stop_loss and signal.signal_type == 'BUY' and signal.stop_loss >= signal.entry_price:
                logger.warning("Signal validation failed: Invalid stop loss for BUY signal")
                return False
            
            if signal.stop_loss and signal.signal_type == 'SELL' and signal.stop_loss <= signal.entry_price:
                logger.warning("Signal validation failed: Invalid stop loss for SELL signal")
                return False
            
            # Check confidence level
            min_confidence = self.config.get('signals.min_confidence', 0.7)
            if signal.confidence < min_confidence:
                logger.warning(f"Signal validation failed: Low confidence {signal.confidence}")
                return False
            
            # Check time since last trade (prevent spam)
            if self.last_trade_time:
                time_diff = (datetime.now() - self.last_trade_time).total_seconds()
                if time_diff < 10:  # Minimum 10 seconds between trades
                    logger.warning("Signal validation failed: Too soon after last trade")
                    return False
            
            logger.info(f"Signal validation passed: {signal.currency_pair} {signal.signal_type}")
            return True
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False
    
    def record_trade(self, signal: ForexSignal, lot_size: float, status: str = "open"):
        """
        Record a trade execution.
        
        Args:
            signal: Executed signal
            lot_size: Trade lot size
            status: Trade status
        """
        try:
            trade_record = TradeRecord(
                timestamp=datetime.now(),
                signal_type=signal.signal_type,
                currency_pair=signal.currency_pair,
                entry_price=signal.entry_price,
                lot_size=lot_size,
                status=status
            )
            
            self.trade_history.append(trade_record)
            self.last_trade_time = datetime.now()
            
            logger.info(f"Trade recorded: {signal.currency_pair} {signal.signal_type} @ {signal.entry_price}")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    def update_trade_result(self, trade_index: int, profit_loss: float, status: str = "closed"):
        """
        Update trade result with profit/loss.
        
        Args:
            trade_index: Index of trade in history
            profit_loss: Profit or loss amount
            status: New trade status
        """
        try:
            if 0 <= trade_index < len(self.trade_history):
                trade = self.trade_history[trade_index]
                trade.profit_loss = profit_loss
                trade.status = status
                
                # Update daily loss
                if profit_loss < 0:
                    self.daily_loss += abs(profit_loss)
                    self.consecutive_losses += 1
                else:
                    self.consecutive_losses = 0
                
                # Check emergency stop
                if self.daily_loss >= self.emergency_stop_loss:
                    self.emergency_stop = True
                    logger.critical(f"EMERGENCY STOP ACTIVATED: Daily loss {self.daily_loss}")
                
                logger.info(f"Trade result updated: P/L {profit_loss}, Status {status}")
                
        except Exception as e:
            logger.error(f"Error updating trade result: {e}")
    
    def _activate_cooldown(self):
        """Activate cooldown period after consecutive losses."""
        self.cooldown_until = datetime.now() + timedelta(seconds=self.cooldown_period)
        logger.warning(f"Cooldown activated until {self.cooldown_until}")
    
    def _get_today_trades_count(self) -> int:
        """Get number of trades executed today."""
        today = datetime.now().date()
        return sum(1 for trade in self.trade_history 
                  if trade.timestamp.date() == today)
    
    def _get_open_trades_count(self) -> int:
        """Get number of currently open trades."""
        return sum(1 for trade in self.trade_history 
                  if trade.status == "open")
    
    def reset_daily_stats(self):
        """Reset daily statistics (call at start of new day)."""
        self.daily_loss = 0.0
        self.consecutive_losses = 0
        logger.info("Daily statistics reset")
    
    def get_safety_status(self) -> Dict:
        """
        Get current safety status.
        
        Returns:
            Dictionary with safety status information
        """
        return {
            'can_trade': self.can_trade(),
            'emergency_stop': self.emergency_stop,
            'daily_loss': self.daily_loss,
            'consecutive_losses': self.consecutive_losses,
            'cooldown_until': self.cooldown_until,
            'today_trades': self._get_today_trades_count(),
            'open_trades': self._get_open_trades_count(),
            'dry_run_mode': self.dry_run_mode
        }
    
    async def monitor_loop(self):
        """Background monitoring loop for safety checks."""
        while True:
            try:
                # Check if we need to reset daily stats
                now = datetime.now()
                if now.hour == 0 and now.minute == 0:
                    self.reset_daily_stats()
                
                # Log safety status periodically
                if now.minute % 15 == 0:  # Every 15 minutes
                    status = self.get_safety_status()
                    logger.info(f"Safety status: {status}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in safety monitor loop: {e}")
                await asyncio.sleep(60)
