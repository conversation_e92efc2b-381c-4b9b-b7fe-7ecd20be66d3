"""
Logging configuration for YouTube Forex Bot
"""

import sys
from pathlib import Path
from loguru import logger


def setup_logging(config_manager=None):
    """
    Setup logging configuration using loguru.
    
    Args:
        config_manager: Optional configuration manager instance
    """
    # Remove default handler
    logger.remove()
    
    # Get configuration values
    if config_manager:
        log_level = config_manager.get('logging.level', 'INFO')
        log_dir = config_manager.get('logging.log_dir', 'logs')
        log_format = config_manager.get('logging.format', 
            "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
        max_size = config_manager.get('logging.max_log_size', '10MB')
        backup_count = config_manager.get('logging.backup_count', 5)
    else:
        log_level = 'INFO'
        log_dir = 'logs'
        log_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
        max_size = '10MB'
        backup_count = 5
    
    # Create log directory
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # Console handler
    logger.add(
        sys.stdout,
        level=log_level,
        format=log_format,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # File handler for all logs
    logger.add(
        f"{log_dir}/youtube_forex_bot.log",
        level=log_level,
        format=log_format,
        rotation=max_size,
        retention=backup_count,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # Error file handler
    logger.add(
        f"{log_dir}/errors.log",
        level="ERROR",
        format=log_format,
        rotation=max_size,
        retention=backup_count,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # Trading specific log
    logger.add(
        f"{log_dir}/trading.log",
        level="INFO",
        format=log_format,
        rotation="1 day",
        retention="30 days",
        compression="zip",
        filter=lambda record: "trading" in record["name"].lower()
    )
    
    logger.info("Logging configured successfully")


def get_logger(name: str):
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)
