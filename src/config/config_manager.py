"""
Configuration Manager for YouTube Forex Bot
Handles loading and managing application configuration.
"""

import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

import yaml
from loguru import logger


class ConfigManager:
    """Manages application configuration from YAML files and environment variables."""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = Path(config_file)
        self._config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """Load configuration from YAML file and environment variables."""
        try:
            # Load from YAML file
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f) or {}
                logger.info(f"Configuration loaded from {self.config_file}")
            else:
                logger.warning(f"Configuration file {self.config_file} not found, using defaults")
                self._config = self._get_default_config()
            
            # Override with environment variables
            self._load_env_overrides()
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            self._config = self._get_default_config()
    
    def _load_env_overrides(self):
        """Load configuration overrides from environment variables."""
        env_mappings = {
            'YOUTUBE_CHANNEL_URL': 'youtube.channel_url',
            'TRADING_ENABLED': 'trading.enabled',
            'DEMO_MODE': 'trading.demo_mode',
            'BROKER_API_KEY': 'trading.broker.api_key',
            'BROKER_LOGIN': 'trading.broker.login',
            'BROKER_PASSWORD': 'trading.broker.password',
            'LOG_LEVEL': 'logging.level',
            'HEADLESS_BROWSER': 'browser.headless',
            'DRY_RUN_MODE': 'safety.dry_run_mode',
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Convert string values to appropriate types
                if env_value.lower() in ('true', 'false'):
                    env_value = env_value.lower() == 'true'
                elif env_value.isdigit():
                    env_value = int(env_value)
                elif self._is_float(env_value):
                    env_value = float(env_value)
                
                self.set(config_path, env_value)
                logger.debug(f"Environment override: {config_path} = {env_value}")
    
    def _is_float(self, value: str) -> bool:
        """Check if string can be converted to float."""
        try:
            float(value)
            return True
        except ValueError:
            return False
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values."""
        return {
            'youtube': {
                'channel_url': 'https://www.youtube.com/@FOREXGOLDforexstrategies',
                'check_interval': 30,
                'max_retries': 3,
                'timeout': 30
            },
            'capture': {
                'interval': 3,
                'save_screenshots': True,
                'screenshot_dir': 'data/screenshots',
                'max_screenshots': 1000
            },
            'trading': {
                'enabled': False,
                'demo_mode': True,
                'max_risk_per_trade': 0.02
            },
            'logging': {
                'level': 'INFO',
                'log_dir': 'logs'
            },
            'safety': {
                'dry_run_mode': True,
                'max_loss_per_day': 100.0
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation (e.g., 'youtube.channel_url')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.warning(f"Error getting config key '{key}': {e}")
            return default
    
    def set(self, key: str, value: Any):
        """
        Set configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation
            value: Value to set
        """
        try:
            keys = key.split('.')
            config = self._config
            
            # Navigate to the parent dictionary
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # Set the value
            config[keys[-1]] = value
            
        except Exception as e:
            logger.error(f"Error setting config key '{key}': {e}")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Dictionary containing section configuration
        """
        return self.get(section, {})
    
    def save(self, file_path: Optional[str] = None):
        """
        Save current configuration to file.
        
        Args:
            file_path: Optional file path, uses default if not provided
        """
        try:
            save_path = Path(file_path) if file_path else self.config_file
            
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def reload(self):
        """Reload configuration from file."""
        self._load_config()
        logger.info("Configuration reloaded")
    
    def validate(self) -> bool:
        """
        Validate configuration values.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required sections
            required_sections = ['youtube', 'capture', 'trading', 'logging']
            for section in required_sections:
                if not self.get(section):
                    logger.error(f"Missing required configuration section: {section}")
                    return False
            
            # Validate specific values
            if self.get('capture.interval', 0) <= 0:
                logger.error("Capture interval must be positive")
                return False
            
            if self.get('trading.max_risk_per_trade', 0) <= 0 or self.get('trading.max_risk_per_trade', 1) > 1:
                logger.error("Max risk per trade must be between 0 and 1")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def __str__(self) -> str:
        """String representation of configuration."""
        return f"ConfigManager(file={self.config_file}, sections={list(self._config.keys())})"
