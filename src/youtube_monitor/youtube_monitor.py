"""
YouTube Monitor for detecting live streams
Monitors specified YouTube channel for live streams and extracts stream URLs.
"""

import asyncio
import re
from typing import Optional, List
from urllib.parse import urljoin, urlparse

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

from loguru import logger


class YouTubeMonitor:
    """Monitors YouTube channel for live streams."""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.driver: Optional[webdriver.Chrome] = None
        self.channel_url = self.config.get('youtube.channel_url')
        self.check_interval = self.config.get('youtube.check_interval', 30)
        self.max_retries = self.config.get('youtube.max_retries', 3)
        self.timeout = self.config.get('youtube.timeout', 30)
        
        logger.info(f"YouTube Monitor initialized for channel: {self.channel_url}")
    
    async def initialize(self):
        """Initialize the YouTube monitor."""
        try:
            await self._setup_driver()
            logger.info("YouTube Monitor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize YouTube Monitor: {e}")
            raise
    
    async def _setup_driver(self):
        """Setup Chrome WebDriver with appropriate options."""
        try:
            chrome_options = Options()
            
            # Get browser options from config
            if self.config.get('browser.headless', False):
                chrome_options.add_argument('--headless')
            
            # Add Chrome options from config
            chrome_opts = self.config.get('browser.chrome_options', [])
            for opt in chrome_opts:
                chrome_options.add_argument(opt)
            
            # Window size
            window_size = self.config.get('browser.window_size', [1920, 1080])
            chrome_options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
            
            # User agent
            user_agent = self.config.get('browser.user_agent')
            if user_agent:
                chrome_options.add_argument(f'--user-agent={user_agent}')
            
            # Download directory
            download_dir = self.config.get('browser.download_dir', 'data/downloads')
            prefs = {
                'download.default_directory': download_dir,
                'download.prompt_for_download': False,
                'download.directory_upgrade': True,
                'safebrowsing.enabled': True
            }
            chrome_options.add_experimental_option('prefs', prefs)
            
            # Setup Chrome service
            service = Service(ChromeDriverManager().install())
            
            # Create driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(self.timeout)
            
            logger.info("Chrome WebDriver setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup Chrome WebDriver: {e}")
            raise
    
    async def get_live_stream_url(self) -> Optional[str]:
        """
        Check if channel has an active live stream and return its URL.
        
        Returns:
            Live stream URL if found, None otherwise
        """
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Checking for live stream (attempt {attempt + 1}/{self.max_retries})")
                
                # Navigate to channel
                await self._navigate_to_channel()
                
                # Look for live stream indicators
                live_url = await self._find_live_stream()
                
                if live_url:
                    logger.info(f"Live stream found: {live_url}")
                    return live_url
                else:
                    logger.debug("No live stream found")
                    return None
                    
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == self.max_retries - 1:
                    logger.error("All attempts to check live stream failed")
                    return None
                
                await asyncio.sleep(2)  # Wait before retry
        
        return None
    
    async def _navigate_to_channel(self):
        """Navigate to the YouTube channel."""
        try:
            # Go to channel live page
            live_url = f"{self.channel_url}/live"
            self.driver.get(live_url)
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            # Accept cookies if prompted
            await self._handle_cookie_consent()
            
        except Exception as e:
            logger.error(f"Failed to navigate to channel: {e}")
            raise
    
    async def _handle_cookie_consent(self):
        """Handle YouTube cookie consent dialog."""
        try:
            # Look for cookie consent buttons
            consent_selectors = [
                "button[aria-label*='Accept']",
                "button[aria-label*='I agree']",
                "button:contains('Accept all')",
                "#yDmH0d > c-wiz > div > div > div > div.NIoIEf > div.G4njw > div.qqtRac > form > div.lssxud > div > button"
            ]
            
            for selector in consent_selectors:
                try:
                    element = WebDriverWait(self.driver, 2).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()
                    logger.debug("Cookie consent accepted")
                    await asyncio.sleep(1)
                    break
                except TimeoutException:
                    continue
                    
        except Exception as e:
            logger.debug(f"No cookie consent dialog found or error handling it: {e}")
    
    async def _find_live_stream(self) -> Optional[str]:
        """Find live stream on the channel page."""
        try:
            # Wait for page content to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Method 1: Look for live badge
            live_indicators = [
                "span.style-scope.ytd-badge-supported-renderer:contains('LIVE')",
                ".badge-style-type-live-now",
                "[aria-label*='live']",
                ".live-badge"
            ]
            
            for indicator in live_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    if elements:
                        logger.debug(f"Found live indicator: {indicator}")
                        return await self._extract_video_url()
                except:
                    continue
            
            # Method 2: Check page URL for live content
            current_url = self.driver.current_url
            if "/watch?v=" in current_url and "live" in current_url.lower():
                logger.debug("Found live stream in URL")
                return current_url
            
            # Method 3: Look for video player with live content
            video_elements = self.driver.find_elements(By.CSS_SELECTOR, "video")
            if video_elements:
                # Check if video is live by looking at page source
                page_source = self.driver.page_source
                if any(keyword in page_source.lower() for keyword in ["live", "streaming", "watching now"]):
                    return await self._extract_video_url()
            
            # Method 4: API-based check (fallback)
            return await self._check_via_api()
            
        except Exception as e:
            logger.error(f"Error finding live stream: {e}")
            return None
    
    async def _extract_video_url(self) -> Optional[str]:
        """Extract video URL from current page."""
        try:
            current_url = self.driver.current_url
            
            # If already on a video page, return current URL
            if "/watch?v=" in current_url:
                return current_url
            
            # Look for video links
            video_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/watch?v=']")
            if video_links:
                href = video_links[0].get_attribute('href')
                if href:
                    return href
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting video URL: {e}")
            return None
    
    async def _check_via_api(self) -> Optional[str]:
        """Check for live stream using YouTube API or web scraping."""
        try:
            # Extract channel ID from URL
            channel_id = await self._extract_channel_id()
            if not channel_id:
                return None
            
            # Use requests to check channel page
            response = requests.get(f"{self.channel_url}/live", timeout=10)
            if response.status_code == 200:
                # Look for live stream indicators in HTML
                html_content = response.text
                
                # Search for video ID in live stream
                video_id_pattern = r'"videoId":"([^"]+)"'
                matches = re.findall(video_id_pattern, html_content)
                
                if matches:
                    video_id = matches[0]
                    # Check if it's actually live
                    if "live" in html_content.lower() or "streaming" in html_content.lower():
                        return f"https://www.youtube.com/watch?v={video_id}"
            
            return None
            
        except Exception as e:
            logger.error(f"Error in API check: {e}")
            return None
    
    async def _extract_channel_id(self) -> Optional[str]:
        """Extract channel ID from channel URL."""
        try:
            # Try to get channel ID from current page
            if self.driver:
                page_source = self.driver.page_source
                channel_id_pattern = r'"channelId":"([^"]+)"'
                matches = re.findall(channel_id_pattern, page_source)
                if matches:
                    return matches[0]
            
            # Extract from URL if possible
            if "/channel/" in self.channel_url:
                return self.channel_url.split("/channel/")[1].split("/")[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting channel ID: {e}")
            return None
    
    async def get_channel_info(self) -> dict:
        """
        Get channel information.
        
        Returns:
            Dictionary with channel information
        """
        try:
            await self._navigate_to_channel()
            
            info = {
                'channel_url': self.channel_url,
                'title': '',
                'subscriber_count': '',
                'is_live': False,
                'live_url': None
            }
            
            # Get channel title
            try:
                title_element = self.driver.find_element(By.CSS_SELECTOR, "#text.ytd-channel-name")
                info['title'] = title_element.text
            except:
                pass
            
            # Get subscriber count
            try:
                sub_element = self.driver.find_element(By.CSS_SELECTOR, "#subscriber-count")
                info['subscriber_count'] = sub_element.text
            except:
                pass
            
            # Check if live
            live_url = await self._find_live_stream()
            if live_url:
                info['is_live'] = True
                info['live_url'] = live_url
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting channel info: {e}")
            return {}
    
    async def close(self):
        """Close the YouTube monitor and cleanup resources."""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
            logger.info("YouTube Monitor closed")
        except Exception as e:
            logger.error(f"Error closing YouTube Monitor: {e}")
