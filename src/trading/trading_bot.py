"""
Trading Bot for executing forex signals
Handles order execution, position management, and risk control.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

import ccxt
from loguru import logger

from src.utils.safety_manager import ForexSignal, TradeRecord


@dataclass
class Position:
    """Represents an open trading position."""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class TradingBot:
    """Handles forex trading operations."""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.exchange: Optional[ccxt.Exchange] = None
        self.positions: Dict[str, Position] = {}
        self.pending_orders: Dict[str, Dict] = {}
        
        # Trading configuration
        self.trading_enabled = self.config.get('trading.enabled', False)
        self.demo_mode = self.config.get('trading.demo_mode', True)
        self.dry_run_mode = self.config.get('safety.dry_run_mode', True)
        
        # Risk management
        self.max_risk_per_trade = self.config.get('trading.max_risk_per_trade', 0.02)
        self.default_lot_size = self.config.get('trading.default_lot_size', 0.01)
        self.slippage_tolerance = self.config.get('trading.slippage_tolerance', 2)
        
        # Account info
        self.account_balance = 0.0
        self.account_equity = 0.0
        self.margin_used = 0.0
        
        logger.info(f"Trading Bot initialized (Enabled: {self.trading_enabled}, Demo: {self.demo_mode})")
    
    async def initialize(self):
        """Initialize the trading bot."""
        try:
            if self.trading_enabled and not self.dry_run_mode:
                await self._setup_exchange()
                await self._load_account_info()
            
            logger.info("Trading Bot initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Trading Bot: {e}")
            if self.trading_enabled:
                raise
    
    async def _setup_exchange(self):
        """Setup trading exchange connection."""
        try:
            broker_config = self.config.get_section('trading.broker')
            broker_name = broker_config.get('name', 'demo_broker')
            
            # For demo purposes, we'll use a mock exchange
            # In production, you would configure actual broker APIs
            if self.demo_mode:
                self.exchange = self._create_demo_exchange()
            else:
                # Configure real broker connection
                # Example for MetaTrader or other brokers
                self.exchange = self._create_real_exchange(broker_config)
            
            logger.info(f"Exchange connection established: {broker_name}")
            
        except Exception as e:
            logger.error(f"Failed to setup exchange: {e}")
            raise
    
    def _create_demo_exchange(self):
        """Create a demo/mock exchange for testing."""
        class DemoExchange:
            def __init__(self):
                self.balance = 10000.0  # Demo balance
                self.positions = {}
                self.orders = {}
                
            async def fetch_balance(self):
                return {
                    'USD': {'free': self.balance, 'used': 0, 'total': self.balance}
                }
            
            async def create_market_order(self, symbol, type, side, amount, price=None, params=None):
                order_id = f"demo_{datetime.now().timestamp()}"
                return {
                    'id': order_id,
                    'symbol': symbol,
                    'type': type,
                    'side': side,
                    'amount': amount,
                    'price': price,
                    'status': 'closed',
                    'filled': amount
                }
            
            async def fetch_ticker(self, symbol):
                # Mock price data
                mock_prices = {
                    'EUR/USD': 1.0850,
                    'GBP/USD': 1.2650,
                    'USD/JPY': 149.50,
                    'XAU/USD': 2050.00
                }
                base_price = mock_prices.get(symbol, 1.0000)
                return {
                    'bid': base_price - 0.0001,
                    'ask': base_price + 0.0001,
                    'last': base_price
                }
        
        return DemoExchange()
    
    def _create_real_exchange(self, broker_config):
        """Create real exchange connection."""
        # This would be implemented based on your broker's API
        # Examples: MetaTrader, Interactive Brokers, etc.
        raise NotImplementedError("Real exchange connection not implemented in demo")
    
    async def _load_account_info(self):
        """Load account information."""
        try:
            if self.exchange:
                balance_info = await self.exchange.fetch_balance()
                self.account_balance = balance_info.get('USD', {}).get('total', 0.0)
                self.account_equity = self.account_balance  # Simplified
                
                logger.info(f"Account loaded - Balance: ${self.account_balance}")
            
        except Exception as e:
            logger.error(f"Failed to load account info: {e}")
    
    async def execute_signal(self, signal: ForexSignal):
        """
        Execute a forex trading signal.
        
        Args:
            signal: ForexSignal to execute
        """
        try:
            logger.info(f"Executing signal: {signal.currency_pair} {signal.signal_type} @ {signal.entry_price}")
            
            if self.dry_run_mode:
                await self._execute_dry_run(signal)
                return
            
            if not self.trading_enabled:
                logger.warning("Trading is disabled, skipping signal execution")
                return
            
            # Calculate position size
            lot_size = await self._calculate_position_size(signal)
            if lot_size <= 0:
                logger.warning("Invalid lot size calculated, skipping trade")
                return
            
            # Execute the trade
            order_result = await self._place_market_order(signal, lot_size)
            
            if order_result:
                # Set stop loss and take profit
                await self._set_stop_loss_take_profit(order_result, signal)
                
                # Record the trade
                await self._record_trade(signal, order_result, lot_size)
                
                logger.info(f"Signal executed successfully: {order_result['id']}")
            else:
                logger.error("Failed to execute signal")
                
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
    
    async def _execute_dry_run(self, signal: ForexSignal):
        """Execute signal in dry run mode (simulation only)."""
        try:
            lot_size = await self._calculate_position_size(signal)
            
            # Simulate order execution
            mock_order = {
                'id': f"dry_run_{datetime.now().timestamp()}",
                'symbol': signal.currency_pair,
                'side': signal.signal_type.lower(),
                'amount': lot_size,
                'price': signal.entry_price,
                'status': 'closed',
                'filled': lot_size
            }
            
            # Log the simulated trade
            logger.info(f"DRY RUN: Would execute {signal.currency_pair} {signal.signal_type} "
                       f"Amount: {lot_size} @ {signal.entry_price}")
            
            if signal.stop_loss:
                logger.info(f"DRY RUN: Would set stop loss at {signal.stop_loss}")
            
            if signal.take_profit:
                logger.info(f"DRY RUN: Would set take profit at {signal.take_profit}")
            
            # Save to performance tracking
            await self._save_performance_data(signal, mock_order)
            
        except Exception as e:
            logger.error(f"Error in dry run execution: {e}")
    
    async def _calculate_position_size(self, signal: ForexSignal) -> float:
        """
        Calculate appropriate position size based on risk management.
        
        Args:
            signal: ForexSignal to calculate size for
            
        Returns:
            Position size in lots
        """
        try:
            # Get current account balance
            if self.account_balance <= 0:
                await self._load_account_info()
            
            # Calculate risk amount
            risk_amount = self.account_balance * self.max_risk_per_trade
            
            # Calculate stop loss distance
            if signal.stop_loss:
                stop_distance = abs(signal.entry_price - signal.stop_loss)
                
                # Calculate position size based on risk
                # This is simplified - real calculation depends on currency pair specifications
                pip_value = 10  # Simplified pip value for major pairs
                position_size = risk_amount / (stop_distance * pip_value * 10000)
                
                # Apply minimum and maximum limits
                position_size = max(0.01, min(position_size, 1.0))
            else:
                # Use default lot size if no stop loss
                position_size = self.default_lot_size
            
            logger.debug(f"Calculated position size: {position_size} lots")
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return self.default_lot_size
    
    async def _place_market_order(self, signal: ForexSignal, lot_size: float) -> Optional[Dict]:
        """Place market order for the signal."""
        try:
            if not self.exchange:
                logger.error("No exchange connection available")
                return None
            
            # Convert signal format to exchange format
            symbol = self._format_symbol_for_exchange(signal.currency_pair)
            side = signal.signal_type.lower()
            
            # Place market order
            order = await self.exchange.create_market_order(
                symbol=symbol,
                type='market',
                side=side,
                amount=lot_size,
                price=None  # Market order
            )
            
            logger.info(f"Market order placed: {order['id']}")
            return order
            
        except Exception as e:
            logger.error(f"Error placing market order: {e}")
            return None
    
    async def _set_stop_loss_take_profit(self, order: Dict, signal: ForexSignal):
        """Set stop loss and take profit orders."""
        try:
            if not self.exchange:
                return
            
            symbol = order['symbol']
            side = 'sell' if order['side'] == 'buy' else 'buy'  # Opposite side for closing
            amount = order['filled']
            
            # Set stop loss
            if signal.stop_loss:
                try:
                    sl_order = await self.exchange.create_order(
                        symbol=symbol,
                        type='stop',
                        side=side,
                        amount=amount,
                        price=signal.stop_loss
                    )
                    logger.info(f"Stop loss set: {sl_order['id']} @ {signal.stop_loss}")
                except Exception as e:
                    logger.error(f"Failed to set stop loss: {e}")
            
            # Set take profit
            if signal.take_profit:
                try:
                    tp_order = await self.exchange.create_order(
                        symbol=symbol,
                        type='limit',
                        side=side,
                        amount=amount,
                        price=signal.take_profit
                    )
                    logger.info(f"Take profit set: {tp_order['id']} @ {signal.take_profit}")
                except Exception as e:
                    logger.error(f"Failed to set take profit: {e}")
                    
        except Exception as e:
            logger.error(f"Error setting stop loss/take profit: {e}")
    
    def _format_symbol_for_exchange(self, currency_pair: str) -> str:
        """Format currency pair for exchange API."""
        # Convert EURUSD to EUR/USD format
        if len(currency_pair) == 6:
            return f"{currency_pair[:3]}/{currency_pair[3:]}"
        return currency_pair
    
    async def _record_trade(self, signal: ForexSignal, order: Dict, lot_size: float):
        """Record trade execution."""
        try:
            trade_record = TradeRecord(
                timestamp=datetime.now(),
                signal_type=signal.signal_type,
                currency_pair=signal.currency_pair,
                entry_price=order.get('price', signal.entry_price),
                lot_size=lot_size,
                status='open'
            )
            
            # Store position
            position = Position(
                id=order['id'],
                symbol=signal.currency_pair,
                side=signal.signal_type.lower(),
                amount=lot_size,
                entry_price=order.get('price', signal.entry_price),
                current_price=order.get('price', signal.entry_price),
                unrealized_pnl=0.0,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit
            )
            
            self.positions[order['id']] = position
            
            logger.info(f"Trade recorded: {order['id']}")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    async def _save_performance_data(self, signal: ForexSignal, order: Dict):
        """Save performance data for analysis."""
        try:
            performance_file = self.config.get('monitoring.performance_file', 'data/performance.json')
            
            data = {
                'timestamp': datetime.now().isoformat(),
                'signal': asdict(signal),
                'order': order,
                'mode': 'dry_run' if self.dry_run_mode else 'live'
            }
            
            # Append to performance file
            try:
                with open(performance_file, 'r') as f:
                    performance_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                performance_data = []
            
            performance_data.append(data)
            
            with open(performance_file, 'w') as f:
                json.dump(performance_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving performance data: {e}")
    
    async def update_positions(self):
        """Update current positions with latest prices."""
        try:
            if not self.exchange or not self.positions:
                return
            
            for position_id, position in self.positions.items():
                try:
                    # Get current price
                    ticker = await self.exchange.fetch_ticker(position.symbol)
                    current_price = ticker['last']
                    
                    # Update position
                    position.current_price = current_price
                    
                    # Calculate unrealized P&L
                    if position.side == 'buy':
                        position.unrealized_pnl = (current_price - position.entry_price) * position.amount
                    else:
                        position.unrealized_pnl = (position.entry_price - current_price) * position.amount
                    
                except Exception as e:
                    logger.error(f"Error updating position {position_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error updating positions: {e}")
    
    async def get_account_status(self) -> Dict:
        """Get current account status."""
        try:
            await self._load_account_info()
            await self.update_positions()
            
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            
            return {
                'balance': self.account_balance,
                'equity': self.account_balance + total_unrealized_pnl,
                'margin_used': self.margin_used,
                'open_positions': len(self.positions),
                'unrealized_pnl': total_unrealized_pnl,
                'positions': [asdict(pos) for pos in self.positions.values()]
            }
            
        except Exception as e:
            logger.error(f"Error getting account status: {e}")
            return {}
    
    async def close(self):
        """Close the trading bot and cleanup."""
        try:
            # Close any open positions if needed
            if self.positions and not self.dry_run_mode:
                logger.info("Closing open positions...")
                # Implementation depends on broker API
            
            self.exchange = None
            logger.info("Trading Bot closed")
            
        except Exception as e:
            logger.error(f"Error closing Trading Bot: {e}")
