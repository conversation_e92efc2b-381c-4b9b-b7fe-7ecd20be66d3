"""
Forex Signal Extractor
Extracts forex trading signals from screenshots using OCR and pattern matching.
"""

import re
import io
from datetime import datetime
from typing import List, Optional, Dict, Tuple
from dataclasses import dataclass

import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
from loguru import logger

from src.utils.safety_manager import ForexSignal


@dataclass
class TextRegion:
    """Represents a text region found in the image."""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    

class ForexSignalExtractor:
    """Extracts forex trading signals from screenshots."""
    
    def __init__(self, config_manager):
        self.config = config_manager
        
        # OCR configuration
        self.ocr_language = self.config.get('ocr.language', 'eng')
        self.ocr_config = self.config.get('ocr.config', '--psm 6')
        self.confidence_threshold = self.config.get('ocr.confidence_threshold', 60)
        
        # Signal configuration
        self.currency_pairs = self.config.get('signals.currency_pairs', [])
        self.signal_keywords = self.config.get('signals.signal_keywords', {})
        self.price_pattern = self.config.get('signals.price_pattern', r'\d+\.\d{2,5}')
        self.min_confidence = self.config.get('signals.min_confidence', 0.7)
        
        # Debug settings
        self.debug_mode = self.config.get('development.debug_mode', False)
        self.save_debug_images = self.config.get('development.save_debug_images', False)
        self.debug_image_dir = self.config.get('development.debug_image_dir', 'data/debug')
        
        if self.save_debug_images:
            import os
            os.makedirs(self.debug_image_dir, exist_ok=True)
        
        logger.info("Forex Signal Extractor initialized")
    
    async def initialize(self):
        """Initialize the signal extractor."""
        try:
            # Test OCR functionality
            test_image = Image.new('RGB', (100, 50), color='white')
            test_text = pytesseract.image_to_string(test_image, config=self.ocr_config)
            
            logger.info("Signal Extractor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Signal Extractor: {e}")
            raise
    
    async def extract_signals(self, image_data: bytes) -> List[ForexSignal]:
        """
        Extract forex signals from screenshot.
        
        Args:
            image_data: Screenshot image as bytes
            
        Returns:
            List of detected forex signals
        """
        try:
            # Convert to PIL Image
            image = Image.open(io.BytesIO(image_data))
            
            # Preprocess image for better OCR
            processed_image = await self._preprocess_image(image)
            
            # Extract text regions
            text_regions = await self._extract_text_regions(processed_image)
            
            # Parse signals from text
            signals = await self._parse_signals_from_text(text_regions)
            
            # Save debug image if enabled
            if self.save_debug_images and signals:
                await self._save_debug_image(processed_image, text_regions, signals)
            
            logger.debug(f"Extracted {len(signals)} signals from image")
            return signals
            
        except Exception as e:
            logger.error(f"Error extracting signals: {e}")
            return []
    
    async def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image for better OCR results.
        
        Args:
            image: Input PIL Image
            
        Returns:
            Preprocessed PIL Image
        """
        try:
            # Convert to grayscale for better OCR
            if image.mode != 'L':
                image = image.convert('L')
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.5)
            
            # Convert to OpenCV format for advanced processing
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_GRAY2BGR)
            
            # Apply Gaussian blur to reduce noise
            cv_image = cv2.GaussianBlur(cv_image, (1, 1), 0)
            
            # Apply morphological operations to clean up text
            kernel = np.ones((1, 1), np.uint8)
            cv_image = cv2.morphologyEx(cv_image, cv2.MORPH_CLOSE, kernel)
            
            # Convert back to PIL
            processed_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY))
            
            return processed_image
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return image
    
    async def _extract_text_regions(self, image: Image.Image) -> List[TextRegion]:
        """
        Extract text regions from image using OCR.
        
        Args:
            image: Preprocessed PIL Image
            
        Returns:
            List of text regions with confidence scores
        """
        try:
            text_regions = []
            
            # Use pytesseract to get detailed OCR data
            ocr_data = pytesseract.image_to_data(
                image, 
                config=self.ocr_config,
                output_type=pytesseract.Output.DICT
            )
            
            # Process OCR results
            for i in range(len(ocr_data['text'])):
                text = ocr_data['text'][i].strip()
                confidence = float(ocr_data['conf'][i])
                
                # Filter out low confidence and empty text
                if confidence >= self.confidence_threshold and text:
                    bbox = (
                        ocr_data['left'][i],
                        ocr_data['top'][i],
                        ocr_data['width'][i],
                        ocr_data['height'][i]
                    )
                    
                    text_region = TextRegion(
                        text=text,
                        confidence=confidence,
                        bbox=bbox
                    )
                    text_regions.append(text_region)
            
            # Also try different PSM modes for better results
            for psm in [6, 7, 8, 13]:
                try:
                    config = f"--psm {psm} {self.ocr_config.split('--psm')[1] if '--psm' in self.ocr_config else ''}"
                    additional_text = pytesseract.image_to_string(image, config=config)
                    
                    if additional_text.strip():
                        # Add as a single region with estimated confidence
                        text_region = TextRegion(
                            text=additional_text.strip(),
                            confidence=75.0,  # Estimated confidence
                            bbox=(0, 0, image.width, image.height)
                        )
                        text_regions.append(text_region)
                        
                except Exception:
                    continue
            
            logger.debug(f"Extracted {len(text_regions)} text regions")
            return text_regions
            
        except Exception as e:
            logger.error(f"Error extracting text regions: {e}")
            return []
    
    async def _parse_signals_from_text(self, text_regions: List[TextRegion]) -> List[ForexSignal]:
        """
        Parse forex signals from extracted text regions.
        
        Args:
            text_regions: List of text regions from OCR
            
        Returns:
            List of parsed forex signals
        """
        try:
            signals = []
            
            # Combine all text for analysis
            all_text = " ".join([region.text for region in text_regions])
            all_text = all_text.upper()
            
            # Find currency pairs
            detected_pairs = []
            for pair in self.currency_pairs:
                if pair.upper() in all_text:
                    detected_pairs.append(pair.upper())
            
            if not detected_pairs:
                logger.debug("No currency pairs detected")
                return signals
            
            # For each detected pair, try to extract signal information
            for pair in detected_pairs:
                signal = await self._extract_signal_for_pair(pair, text_regions, all_text)
                if signal:
                    signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error parsing signals from text: {e}")
            return []
    
    async def _extract_signal_for_pair(self, currency_pair: str, text_regions: List[TextRegion], all_text: str) -> Optional[ForexSignal]:
        """
        Extract signal information for a specific currency pair.
        
        Args:
            currency_pair: Currency pair (e.g., 'EURUSD')
            text_regions: List of text regions
            all_text: Combined text from all regions
            
        Returns:
            ForexSignal if found, None otherwise
        """
        try:
            # Find signal type (BUY/SELL)
            signal_type = None
            buy_keywords = self.signal_keywords.get('buy', ['BUY', 'LONG'])
            sell_keywords = self.signal_keywords.get('sell', ['SELL', 'SHORT'])
            
            for keyword in buy_keywords:
                if keyword in all_text:
                    signal_type = 'BUY'
                    break
            
            if not signal_type:
                for keyword in sell_keywords:
                    if keyword in all_text:
                        signal_type = 'SELL'
                        break
            
            if not signal_type:
                logger.debug(f"No signal type found for {currency_pair}")
                return None
            
            # Extract prices
            prices = self._extract_prices(all_text)
            if not prices:
                logger.debug(f"No prices found for {currency_pair}")
                return None
            
            # Determine entry price (usually the first or most prominent price)
            entry_price = prices[0]
            
            # Extract stop loss and take profit
            stop_loss = self._extract_stop_loss(all_text, prices, signal_type)
            take_profit = self._extract_take_profit(all_text, prices, signal_type)
            
            # Calculate confidence based on completeness and OCR confidence
            confidence = self._calculate_signal_confidence(
                currency_pair, signal_type, entry_price, stop_loss, take_profit, text_regions
            )
            
            if confidence < self.min_confidence:
                logger.debug(f"Signal confidence too low: {confidence}")
                return None
            
            signal = ForexSignal(
                currency_pair=currency_pair,
                signal_type=signal_type,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=confidence,
                timestamp=datetime.now()
            )
            
            logger.info(f"Signal extracted: {currency_pair} {signal_type} @ {entry_price}")
            return signal
            
        except Exception as e:
            logger.error(f"Error extracting signal for {currency_pair}: {e}")
            return None
    
    def _extract_prices(self, text: str) -> List[float]:
        """Extract price values from text."""
        try:
            price_matches = re.findall(self.price_pattern, text)
            prices = []
            
            for match in price_matches:
                try:
                    price = float(match)
                    # Basic validation for forex prices
                    if 0.01 <= price <= 10000:  # Reasonable forex price range
                        prices.append(price)
                except ValueError:
                    continue
            
            return sorted(set(prices))  # Remove duplicates and sort
            
        except Exception as e:
            logger.error(f"Error extracting prices: {e}")
            return []
    
    def _extract_stop_loss(self, text: str, prices: List[float], signal_type: str) -> Optional[float]:
        """Extract stop loss from text."""
        try:
            sl_keywords = self.signal_keywords.get('stop_loss', ['SL', 'STOP LOSS'])
            
            for keyword in sl_keywords:
                if keyword in text:
                    # Look for price near the keyword
                    keyword_index = text.find(keyword)
                    surrounding_text = text[max(0, keyword_index-20):keyword_index+50]
                    
                    sl_prices = re.findall(self.price_pattern, surrounding_text)
                    if sl_prices:
                        sl_price = float(sl_prices[0])
                        
                        # Validate stop loss based on signal type
                        if signal_type == 'BUY' and sl_price < prices[0]:
                            return sl_price
                        elif signal_type == 'SELL' and sl_price > prices[0]:
                            return sl_price
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting stop loss: {e}")
            return None
    
    def _extract_take_profit(self, text: str, prices: List[float], signal_type: str) -> Optional[float]:
        """Extract take profit from text."""
        try:
            tp_keywords = self.signal_keywords.get('take_profit', ['TP', 'TAKE PROFIT'])
            
            for keyword in tp_keywords:
                if keyword in text:
                    # Look for price near the keyword
                    keyword_index = text.find(keyword)
                    surrounding_text = text[max(0, keyword_index-20):keyword_index+50]
                    
                    tp_prices = re.findall(self.price_pattern, surrounding_text)
                    if tp_prices:
                        tp_price = float(tp_prices[0])
                        
                        # Validate take profit based on signal type
                        if signal_type == 'BUY' and tp_price > prices[0]:
                            return tp_price
                        elif signal_type == 'SELL' and tp_price < prices[0]:
                            return tp_price
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting take profit: {e}")
            return None
    
    def _calculate_signal_confidence(self, currency_pair: str, signal_type: str, 
                                   entry_price: float, stop_loss: Optional[float], 
                                   take_profit: Optional[float], text_regions: List[TextRegion]) -> float:
        """Calculate confidence score for the signal."""
        try:
            confidence = 0.0
            
            # Base confidence from OCR
            avg_ocr_confidence = sum(region.confidence for region in text_regions) / len(text_regions) if text_regions else 0
            confidence += (avg_ocr_confidence / 100) * 0.3
            
            # Currency pair detection confidence
            confidence += 0.2
            
            # Signal type detection confidence
            confidence += 0.2
            
            # Entry price confidence
            confidence += 0.15
            
            # Stop loss confidence
            if stop_loss:
                confidence += 0.075
            
            # Take profit confidence
            if take_profit:
                confidence += 0.075
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.0
    
    async def _save_debug_image(self, image: Image.Image, text_regions: List[TextRegion], signals: List[ForexSignal]):
        """Save debug image with annotations."""
        try:
            import os
            from PIL import ImageDraw, ImageFont
            
            # Create annotated image
            debug_image = image.convert('RGB')
            draw = ImageDraw.Draw(debug_image)
            
            # Draw text regions
            for region in text_regions:
                x, y, w, h = region.bbox
                draw.rectangle([x, y, x+w, y+h], outline='red', width=2)
                draw.text((x, y-15), f"{region.text[:20]}...", fill='red')
            
            # Draw signal information
            y_offset = 10
            for signal in signals:
                text = f"{signal.currency_pair} {signal.signal_type} @ {signal.entry_price}"
                draw.text((10, y_offset), text, fill='green')
                y_offset += 20
            
            # Save debug image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_path = os.path.join(self.debug_image_dir, f"debug_{timestamp}.png")
            debug_image.save(debug_path)
            
            logger.debug(f"Debug image saved: {debug_path}")
            
        except Exception as e:
            logger.error(f"Error saving debug image: {e}")
