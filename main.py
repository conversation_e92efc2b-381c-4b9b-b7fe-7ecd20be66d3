#!/usr/bin/env python3
"""
YouTube Forex Trading Bot
Monitors YouTube live streams for forex trading signals and executes trades automatically.

Author: YouTube Forex Bot
Version: 1.0.0
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from src.config.config_manager import ConfigManager
from src.youtube_monitor.youtube_monitor import YouTubeMonitor
from src.screen_capture.screen_capture import ScreenCapture
from src.signal_extractor.signal_extractor import ForexSignalExtractor
from src.trading.trading_bot import TradingBot
from src.utils.logger_config import setup_logging
from src.utils.safety_manager import SafetyManager


class YouTubeForexBot:
    """Main application class for YouTube Forex Bot."""
    
    def __init__(self):
        self.config = ConfigManager()
        self.youtube_monitor: Optional[YouTubeMonitor] = None
        self.screen_capture: Optional[ScreenCapture] = None
        self.signal_extractor: Optional[ForexSignalExtractor] = None
        self.trading_bot: Optional[TradingBot] = None
        self.safety_manager: Optional[SafetyManager] = None
        self.running = False
        
    async def initialize(self):
        """Initialize all components."""
        try:
            logger.info("Initializing YouTube Forex Bot...")
            
            # Create necessary directories
            self._create_directories()
            
            # Initialize components
            self.youtube_monitor = YouTubeMonitor(self.config)
            self.screen_capture = ScreenCapture(self.config)
            self.signal_extractor = ForexSignalExtractor(self.config)
            self.trading_bot = TradingBot(self.config)
            self.safety_manager = SafetyManager(self.config)
            
            # Initialize each component
            await self.youtube_monitor.initialize()
            await self.screen_capture.initialize()
            await self.signal_extractor.initialize()
            await self.trading_bot.initialize()
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            raise
    
    def _create_directories(self):
        """Create necessary directories."""
        directories = [
            "data/screenshots",
            "data/debug",
            "data/downloads",
            "logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    async def start(self):
        """Start the main bot loop."""
        try:
            self.running = True
            logger.info("Starting YouTube Forex Bot main loop...")
            
            # Start monitoring task
            monitor_task = asyncio.create_task(self._monitor_loop())
            
            # Start safety monitoring
            safety_task = asyncio.create_task(self.safety_manager.monitor_loop())
            
            # Wait for tasks to complete
            await asyncio.gather(monitor_task, safety_task)
            
        except asyncio.CancelledError:
            logger.info("Bot loop cancelled")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            raise
    
    async def _monitor_loop(self):
        """Main monitoring loop."""
        capture_interval = self.config.get('capture.interval', 3)
        check_interval = self.config.get('youtube.check_interval', 30)
        
        last_live_check = 0
        current_live_url = None
        
        while self.running:
            try:
                current_time = asyncio.get_event_loop().time()
                
                # Check for live stream periodically
                if current_time - last_live_check >= check_interval:
                    logger.debug("Checking for live stream...")
                    live_url = await self.youtube_monitor.get_live_stream_url()
                    
                    if live_url != current_live_url:
                        if live_url:
                            logger.info(f"New live stream detected: {live_url}")
                            current_live_url = live_url
                            await self.screen_capture.set_stream_url(live_url)
                        else:
                            logger.info("Live stream ended")
                            current_live_url = None
                    
                    last_live_check = current_time
                
                # Process current live stream
                if current_live_url:
                    await self._process_live_stream()
                
                # Wait for next iteration
                await asyncio.sleep(capture_interval)
                
            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _process_live_stream(self):
        """Process the current live stream."""
        try:
            # Capture screenshot
            screenshot = await self.screen_capture.capture_screenshot()
            if not screenshot:
                logger.warning("Failed to capture screenshot")
                return
            
            # Extract signals
            signals = await self.signal_extractor.extract_signals(screenshot)
            if not signals:
                logger.debug("No signals detected in current frame")
                return
            
            logger.info(f"Detected {len(signals)} forex signals")
            
            # Check safety constraints
            if not self.safety_manager.can_trade():
                logger.warning("Trading blocked by safety manager")
                return
            
            # Execute trades
            for signal in signals:
                if self.safety_manager.validate_signal(signal):
                    await self.trading_bot.execute_signal(signal)
                else:
                    logger.warning(f"Signal rejected by safety manager: {signal}")
                    
        except Exception as e:
            logger.error(f"Error processing live stream: {e}")
    
    async def stop(self):
        """Stop the bot gracefully."""
        logger.info("Stopping YouTube Forex Bot...")
        self.running = False
        
        # Close all components
        if self.trading_bot:
            await self.trading_bot.close()
        if self.screen_capture:
            await self.screen_capture.close()
        if self.youtube_monitor:
            await self.youtube_monitor.close()
        
        logger.info("YouTube Forex Bot stopped")


async def main():
    """Main entry point."""
    # Setup logging
    setup_logging()
    
    logger.info("=" * 60)
    logger.info("YouTube Forex Trading Bot v1.0.0")
    logger.info("=" * 60)
    
    bot = YouTubeForexBot()
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(bot.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize and start bot
        await bot.initialize()
        await bot.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        return 1
    finally:
        await bot.stop()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        sys.exit(1)
