# YouTube Forex Bot Makefile

.PHONY: help install test demo live clean logs setup check

# Default target
help:
	@echo "YouTube Forex Bot - Available Commands:"
	@echo "======================================"
	@echo "setup     - Run initial setup"
	@echo "install   - Install dependencies"
	@echo "test      - Run installation tests"
	@echo "check     - Check system requirements"
	@echo "demo      - Run bot in demo mode"
	@echo "live      - Run bot in live trading mode (⚠️ DANGEROUS)"
	@echo "logs      - Show live logs"
	@echo "clean     - Clean temporary files"
	@echo "help      - Show this help message"

# Setup and installation
setup:
	@echo "Running initial setup..."
	python setup.py

install:
	@echo "Installing Python dependencies..."
	pip install -r requirements.txt

test:
	@echo "Running installation tests..."
	python test_installation.py

check:
	@echo "Checking system requirements..."
	@python -c "import sys; print(f'Python: {sys.version}')"
	@tesseract --version 2>/dev/null || echo "Tesseract: Not installed"
	@google-chrome --version 2>/dev/null || chromium-browser --version 2>/dev/null || echo "Chrome: Not found"

# Running the bot
demo:
	@echo "Starting bot in DEMO mode..."
	@echo "⚠️  This is safe mode - no real trading will occur"
	python run_bot.py --demo --verbose

demo-headless:
	@echo "Starting bot in DEMO mode (headless)..."
	python run_bot.py --demo --headless --verbose

live:
	@echo "⚠️⚠️⚠️ WARNING: LIVE TRADING MODE ⚠️⚠️⚠️"
	@echo "This will use REAL MONEY!"
	@read -p "Type 'I UNDERSTAND THE RISKS' to continue: " confirm; \
	if [ "$$confirm" = "I UNDERSTAND THE RISKS" ]; then \
		python run_bot.py --live --verbose; \
	else \
		echo "Cancelled."; \
	fi

# Monitoring
logs:
	@echo "Showing live logs (Ctrl+C to exit)..."
	tail -f logs/youtube_forex_bot.log

logs-error:
	@echo "Showing error logs..."
	tail -f logs/errors.log

logs-trading:
	@echo "Showing trading logs..."
	tail -f logs/trading.log

# Maintenance
clean:
	@echo "Cleaning temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.log.*" -delete
	rm -rf .pytest_cache/
	@echo "Cleanup completed"

clean-data:
	@echo "⚠️  This will delete all captured data!"
	@read -p "Are you sure? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		rm -rf data/screenshots/*; \
		rm -rf data/debug/*; \
		rm -rf data/downloads/*; \
		echo "Data cleaned"; \
	else \
		echo "Cancelled"; \
	fi

# Development
dev-setup:
	@echo "Setting up development environment..."
	pip install -r requirements.txt
	pip install pytest pytest-asyncio black flake8
	@echo "Development setup completed"

format:
	@echo "Formatting code..."
	black src/ *.py

lint:
	@echo "Linting code..."
	flake8 src/ *.py --max-line-length=100

# Configuration
config-demo:
	@echo "Creating demo configuration..."
	@if [ ! -f config_demo.yaml ]; then \
		cp config.yaml config_demo.yaml; \
		sed -i 's/enabled: false/enabled: false/g' config_demo.yaml; \
		sed -i 's/demo_mode: true/demo_mode: true/g' config_demo.yaml; \
		sed -i 's/dry_run_mode: true/dry_run_mode: true/g' config_demo.yaml; \
		echo "Demo config created: config_demo.yaml"; \
	else \
		echo "Demo config already exists"; \
	fi

# Status and information
status:
	@echo "YouTube Forex Bot Status:"
	@echo "========================"
	@echo "Python: $$(python --version)"
	@echo "Working Directory: $$(pwd)"
	@echo "Config File: $$(ls config*.yaml 2>/dev/null || echo 'Not found')"
	@echo "Log Files: $$(ls logs/*.log 2>/dev/null | wc -l) files"
	@echo "Screenshots: $$(ls data/screenshots/*.png 2>/dev/null | wc -l) files"
	@if pgrep -f "main.py" > /dev/null; then \
		echo "Bot Status: RUNNING (PID: $$(pgrep -f main.py))"; \
	else \
		echo "Bot Status: STOPPED"; \
	fi

stop:
	@echo "Stopping bot..."
	@pkill -f "main.py" 2>/dev/null || echo "Bot not running"

# Quick start commands
quick-demo: install demo

quick-test: install test

# Documentation
docs:
	@echo "Opening documentation..."
	@if command -v xdg-open > /dev/null; then \
		xdg-open README.md; \
	elif command -v open > /dev/null; then \
		open README.md; \
	else \
		echo "Please open README.md manually"; \
	fi
