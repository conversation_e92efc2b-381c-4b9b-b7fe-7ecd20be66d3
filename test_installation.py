#!/usr/bin/env python3
"""
Test script to verify YouTube Forex Bot installation
"""

import sys
import subprocess
import importlib
from pathlib import Path


def test_python_version():
    """Test Python version."""
    print("Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} is supported")
        return True
    else:
        print(f"✗ Python {sys.version_info.major}.{sys.version_info.minor} is too old (3.8+ required)")
        return False


def test_required_packages():
    """Test if required packages are installed."""
    print("\nTesting required packages...")
    
    required_packages = [
        'selenium',
        'cv2',
        'pytesseract',
        'PIL',
        'numpy',
        'requests',
        'yaml',
        'loguru',
        'ccxt',
        'pandas'
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\nMissing packages: {', '.join(failed_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True


def test_system_dependencies():
    """Test system dependencies."""
    print("\nTesting system dependencies...")
    
    # Test Tesseract
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✓ Tesseract OCR")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Tesseract OCR not found")
        return False
    
    # Test Chrome/Chromium
    chrome_commands = ['google-chrome', 'chromium-browser', 'chrome', 'chromium']
    chrome_found = False
    
    for cmd in chrome_commands:
        try:
            subprocess.run([cmd, '--version'], 
                         capture_output=True, check=True)
            print("✓ Chrome/Chromium browser")
            chrome_found = True
            break
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    if not chrome_found:
        print("✗ Chrome/Chromium browser not found")
        return False
    
    return True


def test_project_structure():
    """Test project structure."""
    print("\nTesting project structure...")
    
    required_files = [
        'main.py',
        'config.yaml',
        'requirements.txt',
        'src/config/config_manager.py',
        'src/youtube_monitor/youtube_monitor.py',
        'src/screen_capture/screen_capture.py',
        'src/signal_extractor/signal_extractor.py',
        'src/trading/trading_bot.py',
        'src/utils/safety_manager.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\nMissing files: {', '.join(missing_files)}")
        return False
    
    return True


def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from src.config.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # Test basic config access
        channel_url = config.get('youtube.channel_url')
        if channel_url:
            print(f"✓ Configuration loaded")
            print(f"  Channel URL: {channel_url}")
        else:
            print("✗ Configuration missing required values")
            return False
        
        # Test validation
        if config.validate():
            print("✓ Configuration validation passed")
        else:
            print("✗ Configuration validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_modules():
    """Test module imports."""
    print("\nTesting module imports...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        # Test each module
        from src.config.config_manager import ConfigManager
        print("✓ ConfigManager")
        
        from src.utils.safety_manager import SafetyManager, ForexSignal
        print("✓ SafetyManager")
        
        from src.youtube_monitor.youtube_monitor import YouTubeMonitor
        print("✓ YouTubeMonitor")
        
        from src.screen_capture.screen_capture import ScreenCapture
        print("✓ ScreenCapture")
        
        from src.signal_extractor.signal_extractor import ForexSignalExtractor
        print("✓ ForexSignalExtractor")
        
        from src.trading.trading_bot import TradingBot
        print("✓ TradingBot")
        
        return True
        
    except Exception as e:
        print(f"✗ Module import failed: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality."""
    print("\nTesting basic functionality...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from src.config.config_manager import ConfigManager
        from src.utils.safety_manager import SafetyManager, ForexSignal
        from datetime import datetime
        
        # Test config manager
        config = ConfigManager()
        print("✓ Config manager initialization")
        
        # Test safety manager
        safety = SafetyManager(config)
        print("✓ Safety manager initialization")
        
        # Test signal creation
        signal = ForexSignal(
            currency_pair="EURUSD",
            signal_type="BUY",
            entry_price=1.0850,
            stop_loss=1.0800,
            take_profit=1.0900,
            confidence=0.8
        )
        print("✓ Signal creation")
        
        # Test signal validation
        if safety.validate_signal(signal):
            print("✓ Signal validation")
        else:
            print("✗ Signal validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def create_test_directories():
    """Create test directories."""
    print("\nCreating test directories...")
    
    directories = [
        'data/screenshots',
        'data/debug',
        'data/downloads',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ {directory}")


def main():
    """Run all tests."""
    print("YouTube Forex Bot Installation Test")
    print("=" * 40)
    
    tests = [
        ("Python Version", test_python_version),
        ("Required Packages", test_required_packages),
        ("System Dependencies", test_system_dependencies),
        ("Project Structure", test_project_structure),
        ("Configuration", test_configuration),
        ("Module Imports", test_modules),
        ("Basic Functionality", test_basic_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Create directories
    create_test_directories()
    
    # Print summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The bot is ready to run.")
        print("\nNext steps:")
        print("1. Review and edit config.yaml")
        print("2. Run in demo mode: python run_bot.py --demo --verbose")
        print("3. Monitor logs in logs/ directory")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        print("\nTry running: python setup.py")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
