#!/usr/bin/env python3
"""
Quick start script for YouTube Forex Bot
Provides an easy way to run the bot with different configurations.
"""

import argparse
import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from main import main as bot_main


def setup_environment(args):
    """Setup environment variables based on arguments."""
    
    # Set log level
    if args.debug:
        os.environ['LOG_LEVEL'] = 'DEBUG'
    elif args.verbose:
        os.environ['LOG_LEVEL'] = 'INFO'
    else:
        os.environ['LOG_LEVEL'] = 'WARNING'
    
    # Set trading mode
    if args.demo:
        os.environ['DEMO_MODE'] = 'true'
        os.environ['DRY_RUN_MODE'] = 'true'
        os.environ['TRADING_ENABLED'] = 'false'
    elif args.live:
        print("⚠️  WARNING: Live trading mode enabled!")
        response = input("Are you sure you want to enable live trading? (yes/no): ")
        if response.lower() != 'yes':
            print("Exiting...")
            sys.exit(0)
        os.environ['DEMO_MODE'] = 'false'
        os.environ['DRY_RUN_MODE'] = 'false'
        os.environ['TRADING_ENABLED'] = 'true'
    
    # Set browser mode
    if args.headless:
        os.environ['HEADLESS_BROWSER'] = 'true'
    else:
        os.environ['HEADLESS_BROWSER'] = 'false'
    
    # Set channel URL if provided
    if args.channel:
        os.environ['YOUTUBE_CHANNEL_URL'] = args.channel


def print_startup_info(args):
    """Print startup information."""
    print("🤖 YouTube Forex Trading Bot")
    print("=" * 40)
    
    mode = "LIVE TRADING" if args.live else "DEMO MODE"
    print(f"Mode: {mode}")
    
    if args.channel:
        print(f"Channel: {args.channel}")
    else:
        print("Channel: Default (from config)")
    
    browser_mode = "Headless" if args.headless else "Visible"
    print(f"Browser: {browser_mode}")
    
    log_level = "DEBUG" if args.debug else ("INFO" if args.verbose else "WARNING")
    print(f"Log Level: {log_level}")
    
    print("=" * 40)
    
    if args.live:
        print("⚠️  LIVE TRADING MODE - REAL MONEY AT RISK!")
    else:
        print("✅ Demo mode - Safe for testing")
    
    print()


def check_prerequisites():
    """Check if all prerequisites are met."""
    errors = []
    
    # Check if config file exists
    if not Path("config.yaml").exists():
        errors.append("config.yaml not found")
    
    # Check if required directories exist
    required_dirs = ["data", "logs"]
    for directory in required_dirs:
        if not Path(directory).exists():
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Try importing main modules
    try:
        from src.config.config_manager import ConfigManager
        from src.youtube_monitor.youtube_monitor import YouTubeMonitor
    except ImportError as e:
        errors.append(f"Import error: {e}")
    
    if errors:
        print("❌ Prerequisites check failed:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease run: python setup.py")
        return False
    
    return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="YouTube Forex Trading Bot",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_bot.py --demo --verbose
  python run_bot.py --demo --headless --channel "https://www.youtube.com/@example"
  python run_bot.py --live --debug  # ⚠️ LIVE TRADING
        """
    )
    
    # Trading mode
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument(
        '--demo', 
        action='store_true',
        help='Run in demo mode (safe, no real trading)'
    )
    mode_group.add_argument(
        '--live', 
        action='store_true',
        help='Run in live trading mode (⚠️ REAL MONEY AT RISK!)'
    )
    
    # Logging options
    log_group = parser.add_mutually_exclusive_group()
    log_group.add_argument(
        '--debug', 
        action='store_true',
        help='Enable debug logging'
    )
    log_group.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    # Browser options
    parser.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode'
    )
    
    # Channel option
    parser.add_argument(
        '--channel',
        type=str,
        help='YouTube channel URL to monitor'
    )
    
    # Configuration file
    parser.add_argument(
        '--config',
        type=str,
        default='config.yaml',
        help='Configuration file path'
    )
    
    args = parser.parse_args()
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Setup environment
    setup_environment(args)
    
    # Print startup info
    print_startup_info(args)
    
    # Set config file
    if args.config != 'config.yaml':
        os.environ['CONFIG_FILE'] = args.config
    
    try:
        # Run the bot
        exit_code = asyncio.run(bot_main())
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
