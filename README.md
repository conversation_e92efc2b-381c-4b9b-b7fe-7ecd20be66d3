# YouTube Forex Trading Bot

An automated forex trading system that monitors YouTube live streams for trading signals and executes trades based on extracted information.

## ⚠️ IMPORTANT DISCLAIMER

**This software is for educational and research purposes only. Automated trading involves significant financial risk. The authors are not responsible for any financial losses incurred through the use of this software. Always test thoroughly with demo accounts before considering live trading.**

## Features

- **YouTube Channel Monitoring**: Automatically detects live streams on specified forex channels
- **Screen Capture**: Captures screenshots from live streams every 3 seconds
- **OCR Signal Extraction**: Uses advanced OCR to extract forex trading signals from video content
- **Risk Management**: Built-in safety features and risk controls
- **Demo Mode**: Test the system without real money
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

## Requirements

- Python 3.8+
- Chrome/Chromium browser
- Tesseract OCR engine
- Sufficient system resources for video processing

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd youtube_bot_forex
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Tesseract OCR**:
   
   **Ubuntu/Debian**:
   ```bash
   sudo apt-get install tesseract-ocr
   ```
   
   **macOS**:
   ```bash
   brew install tesseract
   ```
   
   **Windows**:
   Download and install from: https://github.com/UB-Mannheim/tesseract/wiki

4. **Install Chrome WebDriver**:
   The application uses `webdriver-manager` to automatically download the appropriate ChromeDriver.

## Configuration

1. **Copy and edit the configuration file**:
   ```bash
   cp config.yaml config_local.yaml
   ```

2. **Key configuration options**:
   - `youtube.channel_url`: Target YouTube channel
   - `trading.enabled`: Enable/disable actual trading
   - `safety.dry_run_mode`: Run in simulation mode
   - `browser.headless`: Run browser in headless mode

3. **Environment variables** (optional):
   ```bash
   export YOUTUBE_CHANNEL_URL="https://www.youtube.com/@FOREXGOLDforexstrategies"
   export DRY_RUN_MODE=true
   export LOG_LEVEL=INFO
   ```

## Usage

### Basic Usage

1. **Start the bot**:
   ```bash
   python main.py
   ```

2. **Monitor logs**:
   ```bash
   tail -f logs/youtube_forex_bot.log
   ```

### Demo Mode (Recommended for testing)

The bot starts in demo mode by default. This allows you to test the system without any financial risk:

```yaml
safety:
  dry_run_mode: true
trading:
  demo_mode: true
  enabled: false
```

### Production Mode (Use with extreme caution)

Only after thorough testing in demo mode:

```yaml
safety:
  dry_run_mode: false
trading:
  demo_mode: false
  enabled: true
  broker:
    # Configure your broker API settings
```

## Project Structure

```
youtube_bot_forex/
├── main.py                 # Application entry point
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── README.md              # This file
├── src/
│   ├── config/            # Configuration management
│   ├── youtube_monitor/   # YouTube monitoring
│   ├── screen_capture/    # Screen capture functionality
│   ├── signal_extractor/  # OCR and signal parsing
│   ├── trading/          # Trading bot implementation
│   └── utils/            # Utility functions
├── data/                 # Data storage
│   ├── screenshots/      # Captured screenshots
│   ├── debug/           # Debug images
│   └── performance.json # Performance tracking
└── logs/                # Log files
```

## Signal Detection

The bot can detect various forex signals including:

- **Currency Pairs**: EUR/USD, GBP/USD, USD/JPY, XAU/USD, etc.
- **Signal Types**: BUY, SELL, LONG, SHORT
- **Price Levels**: Entry prices, stop loss, take profit
- **Keywords**: Configurable signal keywords

## Safety Features

- **Emergency Stop**: Automatic trading halt on excessive losses
- **Daily Loss Limits**: Configurable maximum daily loss
- **Position Limits**: Maximum concurrent positions
- **Consecutive Loss Protection**: Cooldown after consecutive losses
- **Signal Validation**: Multiple validation checks before execution

## Monitoring

- **Real-time Logging**: Comprehensive logging system
- **Performance Tracking**: JSON-based performance data
- **Debug Images**: Optional saving of processed screenshots
- **Account Status**: Regular account balance and position updates

## Troubleshooting

### Common Issues

1. **Chrome Driver Issues**:
   ```bash
   # Update webdriver-manager
   pip install --upgrade webdriver-manager
   ```

2. **OCR Not Working**:
   ```bash
   # Test Tesseract installation
   tesseract --version
   ```

3. **YouTube Access Issues**:
   - Check if the channel URL is correct
   - Verify internet connection
   - Try running with `headless: false` to see browser activity

### Debug Mode

Enable debug mode for detailed troubleshooting:

```yaml
development:
  debug_mode: true
  save_debug_images: true
logging:
  level: DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the logs in `logs/` directory
- Review the configuration in `config.yaml`
- Enable debug mode for detailed information

## Roadmap

- [ ] Support for multiple YouTube channels
- [ ] Advanced signal filtering
- [ ] Machine learning signal validation
- [ ] Web dashboard for monitoring
- [ ] Mobile notifications
- [ ] Backtesting capabilities

---

**Remember**: Always test thoroughly in demo mode before considering live trading. Automated trading carries significant financial risk.
