# YouTube Forex Bot Configuration

# YouTube Channel Settings
youtube:
  channel_url: "https://www.youtube.com/@FOREXGOLDforexstrategies"
  check_interval: 30  # seconds between live stream checks
  max_retries: 3
  timeout: 30

# Screen Capture Settings
capture:
  interval: 3  # seconds between screenshots
  save_screenshots: true
  screenshot_dir: "data/screenshots"
  max_screenshots: 1000  # maximum screenshots to keep
  image_quality: 95
  resize_width: 1920
  resize_height: 1080

# OCR Settings
ocr:
  language: "eng"
  config: "--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz./-:$€£¥"
  confidence_threshold: 60
  preprocessing:
    denoise: true
    sharpen: true
    contrast_enhance: true

# Signal Extraction Settings
signals:
  currency_pairs:
    - "EURUSD"
    - "GBPUSD"
    - "USDJPY"
    - "AUDUSD"
    - "USDCAD"
    - "USDCHF"
    - "NZDUSD"
    - "EURJPY"
    - "GBPJPY"
    - "XAUUSD"  # Gold
    - "XAGUSD"  # Silver
  
  signal_keywords:
    buy: ["BUY", "LONG", "CALL", "UP"]
    sell: ["SELL", "SHORT", "PUT", "DOWN"]
    entry: ["ENTRY", "ENTER", "PRICE"]
    stop_loss: ["SL", "STOP LOSS", "STOPLOSS"]
    take_profit: ["TP", "TAKE PROFIT", "TAKEPROFIT", "TARGET"]
  
  price_pattern: "\\d+\\.\\d{2,5}"  # Regex for price matching
  min_confidence: 0.7

# Trading Settings
trading:
  enabled: false  # Set to true to enable actual trading
  demo_mode: true  # Use demo account for testing
  
  # Risk Management
  max_risk_per_trade: 0.02  # 2% of account balance
  max_daily_trades: 10
  max_concurrent_trades: 3
  
  # Order Settings
  default_lot_size: 0.01
  slippage_tolerance: 2  # pips
  
  # Broker API Settings (example for MetaTrader)
  broker:
    name: "demo_broker"
    server: "demo.server.com"
    login: "your_demo_login"
    password: "your_demo_password"
    api_key: "your_api_key"

# Logging Settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_dir: "logs"
  max_log_size: "10MB"
  backup_count: 5
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# Browser Settings
browser:
  headless: false  # Set to true for production
  window_size: [1920, 1080]
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  download_dir: "data/downloads"
  
  # Chrome options
  chrome_options:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"
    - "--disable-gpu"
    - "--disable-extensions"
    - "--disable-plugins"
    - "--disable-images"  # Faster loading
    - "--mute-audio"

# Safety Settings
safety:
  max_loss_per_day: 100.0  # USD
  emergency_stop_loss: 500.0  # USD
  require_confirmation: true
  dry_run_mode: true  # Test mode without actual trades
  
  # Circuit breakers
  max_consecutive_losses: 5
  cooldown_period: 300  # seconds after max losses

# Monitoring Settings
monitoring:
  enable_gui: false
  save_performance_data: true
  performance_file: "data/performance.json"
  alert_on_errors: true
  
  # Notifications (optional)
  notifications:
    email:
      enabled: false
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "your_app_password"
    
    telegram:
      enabled: false
      bot_token: "your_bot_token"
      chat_id: "your_chat_id"

# Development Settings
development:
  debug_mode: true
  save_debug_images: true
  debug_image_dir: "data/debug"
  profile_performance: false
